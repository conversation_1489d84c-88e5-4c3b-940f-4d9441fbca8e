/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity.coretransfers;

import static org.junit.jupiter.api.Assertions.*;

import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersActivityMapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class CoreTransfersActivityMapperTest {

  private CoreTransfersActivityMapper mapper;

  @BeforeEach
  void setUp() {
    mapper = new CoreTransfersActivityMapper();
  }

  @Test
  void mapCoreTransferActivities_WithSuccessStatusAndSameDates_ShouldMapToCompletedActivity() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(List.of(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);
    assertEquals("Completed", bffActivity.getDisplayStatus());
    assertEquals("3", bffActivity.getStatus());
    assertEquals("REF123", bffActivity.getId());
    assertEquals("REF123", bffActivity.getDisplayId());
    assertEquals("FROM_ACCOUNT", bffActivity.getFromAccountId());
    assertEquals("TO_ACCOUNT", bffActivity.getToAccountId());
    assertEquals("CHECKING", bffActivity.getToAccountType());
    assertEquals(100.50, bffActivity.getAmount());
    assertEquals(LocalDate.of(2024, 1, 15), bffActivity.getCreationDate());
    assertEquals(LocalDate.of(2024, 1, 15), bffActivity.getDueDate());
    assertEquals(ActivityBase.INTERNAL_TRANSFER, bffActivity.getActivityType());
    assertEquals(ActivityBase.INTERNAL_TRANSFER, bffActivity.getType());

    // Account details should be null when no account list is provided
    assertNull(bffActivity.getFromAccountNumber());
    assertNull(bffActivity.getFromAccountName());
    assertNull(bffActivity.getToAccountNumber());
    assertNull(bffActivity.getToAccountName());
  }

  @Test
  void mapCoreTransferActivities_WithSuccessStatus_ShouldMapToCompletedActivityRegardlessOfDates() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 16));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);
    assertEquals("Completed", bffActivity.getDisplayStatus());
    assertEquals("3", bffActivity.getStatus());
  }

  @Test
  void mapCoreTransferActivities_WithFailureStatus_ShouldMapToUnsuccessfulActivity() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity(
            "VALIDATION_FAILURE", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);
    assertEquals("Unsuccessful", bffActivity.getDisplayStatus());
    assertEquals("1", bffActivity.getStatus());
  }

  @Test
  void mapCoreTransferActivities_WithMultipleActivities_ShouldMapAllCorrectly() {
    // Given
    TDSCoreTransferActivity successActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));
    TDSCoreTransferActivity failureActivity =
        createCoreTransferActivity(
            "INVALID_ACC_SOURCE", LocalDate.of(2024, 1, 16), LocalDate.of(2024, 1, 16));

    List<TDSCoreTransferActivity> coreActivities = Arrays.asList(successActivity, failureActivity);

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(coreActivities);

    // Then
    assertEquals(2, result.size());
    assertEquals("Completed", result.get(0).getDisplayStatus());
    assertEquals("Unsuccessful", result.get(1).getDisplayStatus());
  }

  @Test
  void mapCoreTransferActivities_ShouldSetDefaultValuesForUnmappableFields() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    BffActivity bffActivity = result.get(0);
    // Account details should be null when no account list is provided
    assertNull(bffActivity.getFromAccountNumber());
    assertNull(bffActivity.getFromAccountName());
    assertNull(bffActivity.getToAccountNumber());
    assertNull(bffActivity.getToAccountName());
    assertFalse(bffActivity.isExpressDelivery());
    assertNull(bffActivity.getAdditionalPrincipleAmount());
    assertNull(bffActivity.getMemo());
    assertNull(bffActivity.getDeliveryDate());
    assertNull(bffActivity.getEndDate());
    assertNull(bffActivity.getFrequency());
    assertNull(bffActivity.getNumberOfActivities());
    assertNull(bffActivity.getNumberOfRemainingActivities());
    assertFalse(bffActivity.isEditable());
    assertFalse(bffActivity.isCancelable());
    assertFalse(bffActivity.isSeriesTemplate());
  }

  @Test
  void mapCoreTransferActivities_WithNullAmount_ShouldHandleGracefully() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));
    coreActivity.setAmount(null);

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    assertNull(result.get(0).getAmount());
  }

  @Test
  void mapCoreTransferActivities_WithNullStatus_ShouldMapToInProcessActivity() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity(null, LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);
    assertEquals(
        "Completed",
        bffActivity
            .getDisplayStatus()); // In Process status in core transfers is treated as Completed in
    // BFF
    assertEquals("3", bffActivity.getStatus());
  }

  @Test
  void mapCoreTransferActivities_WithNullDates_ShouldHandleGracefully() {
    // Given
    TDSCoreTransferActivity coreActivity = createCoreTransferActivity("SUCCESS", null, null);

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);
    assertNull(bffActivity.getCreationDate());
    assertNull(bffActivity.getDueDate());
    assertNull(bffActivity.getCreateTimestamp());
    assertEquals(
        "Completed",
        bffActivity.getDisplayStatus()); // SUCCESS status → Completed regardless of dates
    assertEquals("3", bffActivity.getStatus());
  }

  @Test
  void mapCoreTransferActivities_WithEmptyAccountList_ShouldSetAccountDetailsToNull() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    // When - pass empty account list
    List<BffActivity> result =
        mapper.mapCoreTransferActivities(Arrays.asList(coreActivity), new ArrayList<>());

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);

    // Account details should be null when no accounts are provided
    assertNull(bffActivity.getFromAccountNumber());
    assertNull(bffActivity.getFromAccountName());
    assertNull(bffActivity.getToAccountNumber());
    assertNull(bffActivity.getToAccountName());

    // But other fields should still be mapped correctly
    assertEquals("Completed", bffActivity.getDisplayStatus());
    assertEquals("REF123", bffActivity.getId());
  }

  @Test
  void mapCoreTransferActivities_WithValidAccountList_ShouldPopulateAccountDetails() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    List<com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount>
        accountList = createMockAccountList();

    // When - pass valid account list
    List<BffActivity> result =
        mapper.mapCoreTransferActivities(Arrays.asList(coreActivity), accountList);

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);

    // Account details should be populated from the account list
    assertEquals("****1234", bffActivity.getFromAccountNumber());
    assertEquals("My Checking Account", bffActivity.getFromAccountName());
    assertEquals("****5678", bffActivity.getToAccountNumber());
    assertEquals("My Savings Account", bffActivity.getToAccountName());

    // Other fields should still be mapped correctly
    assertEquals("Completed", bffActivity.getDisplayStatus());
    assertEquals("REF123", bffActivity.getId());
  }

  private List<com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount>
      createMockAccountList() {
    var fromAccount =
        new com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount();
    fromAccount.setId("FROM_ACCOUNT");
    fromAccount.setDisplayAccountNumber("****1234");
    fromAccount.setDisplayName("My Checking Account");

    var toAccount =
        new com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount();
    toAccount.setId("TO_ACCOUNT");
    toAccount.setDisplayAccountNumber("****5678");
    toAccount.setDisplayName("My Savings Account");

    return Arrays.asList(fromAccount, toAccount);
  }

  private TDSCoreTransferActivity createCoreTransferActivity(
      String transferStatus, LocalDate createdDate, LocalDate expectedPostingDate) {
    TDSCoreTransferActivity activity = new TDSCoreTransferActivity();
    activity.setReferenceId("REF123");
    activity.setFromAccountId("FROM_ACCOUNT");
    activity.setFromAccountType("SAVINGS");
    activity.setToAccountId("TO_ACCOUNT");
    activity.setToAccountType("CHECKING");
    activity.setAmount(new BigDecimal("100.50"));
    activity.setTransferStatus(transferStatus);
    activity.setCreatedDate(createdDate);
    activity.setExpectedPostingDate(expectedPostingDate);
    return activity;
  }
}
